<template>
  <!-- 列表 -->
  <ContentWrap>
    <el-tabs v-model="subTabsName">
      <el-tab-pane label="待入库" name="pendingReceipt">
        <el-table
          v-loading="workOrderLoading"
          :data="workOrderList"
          :stripe="true"
          border
          :show-overflow-tooltip="true"
          highlight-current-row
          show-summary
          :summary-method="workOrderSummaryMethod"
          @selection-change="handleWorkOrderSelectionChange"
          style="width: 100%"
        >
          <el-table-column label="生产单号" align="left" prop="workNo" width="140" fixed="left">
            <template #default="scope">
              <div class="work-no-container">
                <div class="order-no-cell">
                  <div class="order-no-content">
                    <span class="work-no-text">{{ scope.row.workNo }}</span>
                  </div>
                  <el-button
                    link
                    type="info"
                    @click="copyOrderNo(scope.row.workNo)"
                    class="copy-btn copy-btn-fixed"
                    size="small"
                  >
                    <Icon icon="ep:copy-document" :size="12"/>
                  </el-button>
                </div>
                <dict-tag
                  v-if="scope.row.approveStatus"
                  :type="DICT_TYPE.APPROVE_STATUS"
                  :value="scope.row.approveStatus"
                  class="status-tag"
                />
              </div>
            </template>
          </el-table-column>
          <el-table-column label="产品编号" align="center" prop="productCode" width="120" fixed="left"/>
          <el-table-column label="产品名称" align="center" prop="productName" width="140" fixed="left"/>
          <el-table-column label="规格" align="center" prop="spec" />
          <el-table-column label="订单数量" align="center" prop="orderQuantity" width="110px">
            <template #default="scope">
              <el-tag>{{ scope.row.orderQuantity || 0 }} {{ getWorkOrderUnitName(scope.row.orderUnit) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="计划数量" align="center" prop="scheduleQuantity" width="110">
            <template  #default="scope">
              <el-tag>
                {{ scope.row.scheduleQuantity || 0 }} {{ getWorkOrderUnitName(scope.row.orderUnit) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="计划件数" align="center" prop="schedulePiece" width="100"/>



          <el-table-column
            label="开始时间"
            align="center"
            prop="scheduleStartTime"
            :formatter="dateFormatter"
            width="180px"
          />
          <el-table-column
            label="结束时间"
            align="center"
            prop="scheduleEndTime"
            :formatter="dateFormatter"
            width="180px"
          />
          <el-table-column label="来源类型" align="center" prop="orderType" width="120">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.MFG_ORDER_SOURCE" :value="scope.row.orderType" />
            </template>
          </el-table-column>
          <el-table-column label="来源单号" align="center" prop="orderNo" width="120">
            <template #default="scope">
              <div class="order-no-cell" v-if="scope.row.orderNo">
                <div class="order-no-content">
                  <span>{{ scope.row.orderNo }}</span>
                </div>
                <el-button
                  link
                  type="info"
                  @click="copyOrderNo(scope.row.orderNo)"
                  class="copy-btn copy-btn-fixed"
                  size="small"
                >
                  <Icon icon="ep:copy-document" :size="12"/>
                </el-button>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="bom" align="center" prop="bomCode">
            <template #default="scope">
              <el-tag v-if="scope.row.bomCode">{{ scope.row.bomCode || '无' }} {{ scope.row.bomVersion }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center" prop="status" width="100">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.WORK_ORDER_STATUS" :value="scope.row.status" />
            </template>
          </el-table-column>
          <el-table-column label="领料状态" align="center" prop="pickingStatus" width="100">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.COMMON_TASK_STATUS" :value="scope.row.pickingStatus" />
            </template>
          </el-table-column>
          <el-table-column label="入库状态" align="center" prop="inStockStatus" width="100">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.COMMON_TASK_STATUS" :value="scope.row.inStockStatus" />
            </template>
          </el-table-column>
          <el-table-column label="报工状态" align="center" prop="reportStatus" width="100">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.COMMON_TASK_STATUS" :value="scope.row.reportStatus" />
            </template>
          </el-table-column>
          <el-table-column label="质检状态" align="center" prop="qualityStatus" width="100">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.COMMON_TASK_STATUS" :value="scope.row.qualityStatus" />
            </template>
          </el-table-column>
          <el-table-column
            label="下单时间"
            align="center"
            prop="orderDate"
            :formatter="dateFormatter2"
            width="100px"
          />
          <el-table-column
            label="交期"
            align="center"
            prop="deliverDate"
            :formatter="dateFormatter"
            width="180px"
          />
          <el-table-column label="计划用时" align="center" prop="scheduleCostTime" width="100"/>
          <el-table-column label="计划产线" align="center" prop="scheduleLine" width="100">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.MANUFACTURE_LINE" :value="scope.row.scheduleLine" />
            </template>
          </el-table-column>
          <el-table-column label="计划用人" align="center" prop="scheduleHeadcount" width="100"/>
          <el-table-column label="生产要求" align="center" prop="requirement" width="100"/>
          <el-table-column label="备注" align="center" prop="remark" />
          <el-table-column label="完成进度" align="center" prop="progress" width="100"/>
          <el-table-column label="实际生产线" align="center" prop="actualLine" width="100">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.MANUFACTURE_LINE" :value="scope.row.actualLine" />
            </template>
          </el-table-column>
          <el-table-column label="实际生产数量" align="center" prop="actualQuantity" width="120"/>
          <el-table-column
            label="实际开始时间"
            align="center"
            prop="actualStartTime"
            :formatter="dateFormatter"
            width="180px"
          />
          <el-table-column
            label="实际结束时间"
            align="center"
            prop="actualEndTime"
            :formatter="dateFormatter"
            width="180px"
          />
          <el-table-column label="实际耗时" align="center" prop="actualCostTime" width="100"/>
          <el-table-column label="实际用人" align="center" prop="actualHeadcount" width="100"/>
          <el-table-column label="实际生产件数" align="center" prop="actualPiece" width="120"/>
          <el-table-column label="批号" align="center" prop="actualBatchNo" />
          <el-table-column label="生产备注" align="center" prop="actualRemark" width="100"/>
          <el-table-column
            label="创建时间"
            align="center"
            prop="createTime"
            :formatter="dateFormatter"
            width="180px"
          />
          <el-table-column label="审批单号" align="center" prop="approveNo" width="120"/>
          <el-table-column
            label="审批时间"
            align="center"
            prop="approvalTime"
            :formatter="dateFormatter"
            width="180px"
          />

          <!-- 操作列 -->
          <el-table-column label="操作" align="center" width="120" fixed="right">
            <template #default="scope">
              <el-button
                link
                type="primary"
                @click="openProductReceiptForm('create', scope.row)"
                v-hasPermi="['inventory:product-receipt:create']"
              >
                入库
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <Pagination
          :total="workOrderTotal"
          v-model:page="workOrderQueryParams.pageNo"
          v-model:limit="workOrderQueryParams.pageSize"
          @pagination="getWorkOrderList"
        />
      </el-tab-pane>
      <el-tab-pane label="已入库" name="received">
        <el-card>
          <!-- 搜索工作栏 -->
          <el-form
            class="-mb-15px"
            :model="queryParams"
            ref="queryFormRef"
            :inline="true"
            label-width="auto"
          >
      <!-- 基础搜索项 - 始终显示 -->
      <el-form-item label="单号" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          placeholder="请输入单号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="产品名称" prop="materialName">
        <el-input
          v-model="queryParams.materialName"
          placeholder="请输入产品名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="产品编号" prop="materialCode">
        <el-input
          v-model="queryParams.materialCode"
          placeholder="请输入产品名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <!-- 高级搜索项 - 可展开收起 -->
      <template v-if="isExpanded">
        <el-form-item label="来源类型" prop="sourceType">
          <el-select
            v-model="queryParams.sourceType"
            placeholder="请选择来源类型"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="item in material_source"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="交易对象名称" prop="objectName">
          <el-input
            v-model="queryParams.objectName"
            placeholder="请输入交易对象名称"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="业务类型" prop="bizType">
          <el-select
            v-model="queryParams.bizType"
            placeholder="请选择业务类型"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="item in inventory_transaction_type"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="来源单编号" prop="sourceNo">
          <el-input
            v-model="queryParams.sourceNo"
            placeholder="请输入来源单编号"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="交易对象订单号" prop="objectOrderNo">
          <el-input
            v-model="queryParams.objectOrderNo"
            placeholder="请输入交易对象订单号"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="交易日期" prop="date">
          <el-date-picker
            v-model="queryParams.date"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item>
        <!-- <el-form-item label="摘要" prop="note">
          <el-input
            v-model="queryParams.note"
            placeholder="请输入摘要"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="queryParams.remark"
            placeholder="请输入备注"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item> -->
        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker
            v-model="queryParams.createTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="审批状态" prop="approveStatus">
          <el-select
            v-model="queryParams.approveStatus"
            placeholder="请选择审批状态"
            clearable
            class="!w-240px"
          >
            <el-option
            v-for="item in approve_status"
            :key="item.value"
            :label="item.label"
            :value="item.value"
            />
          </el-select>
        </el-form-item>

      </template>

      <!-- 操作按钮行 -->
      <el-form-item>
        <el-button @click="handleQuery" class="ml-4"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['inventory:product-receipt:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['inventory:product-receipt:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
        <el-button
          type="primary"
          plain
          @click="handleApproval"
          :loading="exportLoading"
          v-hasPermi="['inventory:product-receipt:approve']"
        >
          <Icon icon="ep:check" class="mr-5px" /> 审核
        </el-button>
        <el-button
          type="text"
          @click="toggleExpanded"
          class="ml-2"
        >
          {{ isExpanded ? '收起' : '展开' }}
          <Icon :icon="isExpanded ? 'ep:arrow-up' : 'ep:arrow-down'" class="ml-1" />
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 列表 -->
    <el-table
      v-loading="loading"
      :data="flattenedList"
      :stripe="true"
      border
      :show-overflow-tooltip="true"
      highlight-current-row
      show-summary
      :summary-method="summaryMethod"
      @current-change="handleCurrentChange"
      @selection-change="handleSelectionChange"
      :span-method="objectSpanMethod"
      style="width: 100%"
    >
      <el-table-column type="selection" width="60" fixed="left" />
      <!-- 最重要的主单据信息 -->
      <el-table-column label="单号" align="left" prop="orderNo" width="180px" fixed="left">
        <template #default="scope">
          <div class="order-no-container">
            <div class="order-no-cell">
              <div class="order-no-content">
                <el-link
                  type="primary"
                  @click="openDetail(scope.row.id, scope.row.orderNo)"
                  v-hasPermi="['inventory:product-receipt:query']"
                >
                  {{ scope.row.orderNo }}
                </el-link>
              </div>
              <el-button
                link
                type="info"
                @click="copyOrderNo(scope.row.orderNo)"
                class="copy-btn copy-btn-fixed"
                size="small"
              >
                <Icon icon="ep:copy-document" :size="12"/>
              </el-button>
            </div>
            <dict-tag
              v-if="scope.row.approveStatus"
              :type="DICT_TYPE.APPROVE_STATUS"
              :value="scope.row.approveStatus"
              class="status-tag"
            />
          </div>
        </template>
      </el-table-column>
<!--      <el-table-column label="业务类型" align="left" prop="bizType" width="100px" fixed="left">
        <template #default="scope">
          {{ getDictLabel(DICT_TYPE.SCM_BIZ_TYPE, scope.row.bizType) }}
        </template>
      </el-table-column>-->

      <!-- 核心明细信息 - 最重要的业务数据 -->
      <el-table-column label="产品名称" align="left" prop="detail.materialName" width="180px" fixed="left"/>
      <el-table-column label="产品编号" align="left" prop="detail.materialCode" width="120px" />
      <el-table-column label="产品规格" align="left" prop="detail.spec" width="100px"/>
      <el-table-column label="实收数量" align="right" prop="detail.fulfilledQuantity" width="100px">
        <template #default="scope">
          {{ formatQuantity(scope.row.detail.fulfilledQuantity) }}{{ getUnitName(scope.row.detail?.unit) || scope.row.detail?.unit || '' }}
        </template>
      </el-table-column>
      <el-table-column label="应收数量" align="right" prop="detail.plannedQuantity" width="100px">
        <template #default="scope">
          {{ formatQuantity(scope.row.detail.plannedQuantity) }}{{ getUnitName(scope.row.detail?.unit) || scope.row.detail?.unit || '' }}
        </template>
      </el-table-column>
      <!-- <el-table-column label="单位" align="center" width="80px">
        <template #default="scope">
          {{ getUnitName(scope.row.detail?.unit) || scope.row.detail?.unit || '' }}
        </template>
      </el-table-column> -->
      <el-table-column label="单价" align="right" prop="detail.unitPrice" width="100px">
        <template #default="scope">
          {{ formatAmount(scope.row.detail.unitPrice) }}
        </template>
      </el-table-column>
      <el-table-column label="金额" align="right" prop="detail.amount" width="100px">
        <template #default="scope">
          {{ formatAmount(scope.row.detail.amount) }}
        </template>
      </el-table-column>
      <el-table-column label="批号" align="left" prop="detail.batchNo" width="100px"/>



      <!-- 重要的主单据业务信息 -->
      <el-table-column label="来源类型" align="center" prop="sourceType" width="100px">
        <template #default="scope">
          {{ getDictLabel(DICT_TYPE.PRODUCT_SOURCE_TYPE, scope.row.sourceType) }}
        </template>
      </el-table-column>
      <el-table-column label="来源单编号" align="left" prop="sourceNo" width="120px">
        <template #default="scope">
          <div class="order-no-cell" v-if="scope.row.sourceNo">
            <div class="order-no-content">
              <span>{{ scope.row.sourceNo }}</span>
            </div>
            <el-button
              link
              type="info"
              @click="copyOrderNo(scope.row.sourceNo)"
              class="copy-btn copy-btn-fixed"
              size="small"
            >
              <Icon icon="ep:copy-document" :size="12"/>
            </el-button>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="交易对象名称" align="left" prop="objectName" width="150px"/>
      <el-table-column
        label="交易日期"
        align="center"
        prop="date"
        :formatter="dateFormatter"
        width="100px"
      />

      <!-- 税务和财务信息 -->
      <el-table-column label="含税单价" align="right" prop="detail.taxPrice" width="100px">
        <template #default="scope">
          {{ formatAmount(scope.row.detail.taxPrice) }}
        </template>
      </el-table-column>
      <el-table-column label="含税金额" align="right" prop="detail.taxAmount" width="110px">
        <template #default="scope">
          {{ formatAmount(scope.row.detail.taxAmount) }}
        </template>
      </el-table-column>

      <!-- 基本单位信息 -->
      <!-- <el-table-column label="基本单位" align="center" width="100px">
        <template #default="scope">
          {{ getUnitName(scope.row.detail?.standardUnit) || scope.row.detail?.standardUnit || '' }}
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="基本单位实收数量" align="right" prop="detail.standardFulfilledQuantity" width="140px">
        <template #default="scope">
          {{ formatQuantity(scope.row.detail.standardFulfilledQuantity) }}
        </template>
      </el-table-column>
      <el-table-column label="基本单位应收数量" align="right" prop="detail.standardPlannedQuantity" width="140px">
        <template #default="scope">
          {{ formatQuantity(scope.row.detail.standardPlannedQuantity) }}
        </template>
      </el-table-column> -->

      <!-- 开票信息 -->
      <!-- <el-table-column label="开票数量" align="right" prop="detail.invoiceQuantity" width="100px">
        <template #default="scope">
          {{ formatQuantity(scope.row.detail.invoiceQuantity) }}
        </template>
      </el-table-column>
      <el-table-column label="开票金额" align="right" prop="detail.invoiceAmount" width="100px">
        <template #default="scope">
          {{ formatAmount(scope.row.detail.invoiceAmount) }}
        </template>
      </el-table-column> -->

      <!-- 日期信息 -->
      <el-table-column
        label="生产日期"
        align="center"
        prop="detail.effictiveDate"
        :formatter="dateFormatter"
        width="100px"
      />
      <el-table-column
        label="失效日期"
        align="center"
        prop="detail.expiryDate"
        :formatter="dateFormatter"
        width="100px"
      />

      <!-- 其他主单据信息 -->
      <el-table-column label="交易对象订单号" align="left" prop="objectOrderNo" width="140px"/>
      <el-table-column label="摘要" align="left" prop="note" width="120px"/>
      <el-table-column label="备注" align="left" prop="remark" width="120px"/>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />

      <!-- 详细的明细信息 -->
      <!-- <el-table-column label="开票基本数量" align="right" prop="detail.standardInvoiceQuantity" width="120px"/> -->
      <el-table-column label="明细备注" align="left" prop="detail.remark" width="120px"/>
      <el-table-column label="说明" align="left" prop="detail.note" width="120px"/>

      <!-- 系统和标识信息 -->
      <!-- <el-table-column label="明细ID" align="center" prop="detail.id" width="80px" /> -->
      <el-table-column label="明细单号" align="left" prop="detail.bizOrderNo" width="120px">
        <template #default="scope">
          <div class="order-no-cell" v-if="scope.row.detail.bizOrderNo">
            <div class="order-no-content">
              <span>{{ scope.row.detail.bizOrderNo }}</span>
            </div>
            <el-button
              link
              type="info"
              @click="copyOrderNo(scope.row.detail.bizOrderNo)"
              class="copy-btn copy-btn-fixed"
              size="small"
            >
              <Icon icon="ep:copy-document" :size="12"/>
            </el-button>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="仓库ID" align="center" prop="detail.warehouseId" width="80px" />
      <el-table-column label="库位ID" align="center" prop="detail.locationId" width="80px" />
      <el-table-column label="产品ID" align="center" prop="detail.materialId" width="80px" />
      <el-table-column label="源单ID" align="center" prop="detail.sourceId" width="80px"/> -->
      <el-table-column label="源单单号" align="left" prop="detail.sourceNo" width="120px">
        <template #default="scope">
          <div class="order-no-cell" v-if="scope.row.detail.sourceNo">
            <div class="order-no-content">
              <span>{{ scope.row.detail.sourceNo }}</span>
            </div>
            <el-button
              link
              type="info"
              @click="copyOrderNo(scope.row.detail.sourceNo)"
              class="copy-btn copy-btn-fixed"
              size="small"
            >
              <Icon icon="ep:copy-document" :size="12"/>
            </el-button>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="成本对象编码" align="left" prop="detail.costObjectId" width="120px"/>
      <el-table-column label="成本对象名称" align="left" prop="detail.costObjectName" width="120px"/>
      <el-table-column label="记账凭证号" align="left" prop="detail.accountingVoucherNumber" width="120px"/> -->
      <el-table-column
        label="明细创建时间"
        align="center"
        prop="detail.createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="120px" fixed="right" prop="operation">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['inventory:product-receipt:update']"
            v-if="scope.row.approveStatus !== 3"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['inventory:product-receipt:delete']"
            v-if="scope.row.approveStatus !== 3"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
        </el-table>
        <!-- 分页 -->
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ProductReceiptForm ref="formRef" @success="getList" />
  <!-- 审核弹窗 -->
  <ApproveInfoForm ref="approveInfoFormRef" @success="getList" :biz-id="currentRow?.id" biz-type="product_receipt" :biz-no="currentRow?.orderNo"/>
</template>

<script setup lang="ts">
import { dateFormatter, dateFormatter2 } from '@/utils/formatTime'
import download from '@/utils/download'
import { ProductReceiptApi, ProductReceiptVO } from '@/api/scm/inventory/productreceipt'
import { getRemoteUnit } from '@/utils/commonBiz'
import ProductReceiptForm from './ProductReceiptForm.vue'
import ApproveInfoForm from '../../base/approveinfo/ApproveInfoForm.vue'
import { DICT_TYPE,getStrDictOptions,getDictLabel } from '@/utils/dict'
import { amountTableFormatter, quantityTableFormatter, formatAmount, formatQuantity } from '@/utils/formatter'
import { useClipboard } from '@vueuse/core'
import { WorkOrderApi, WorkOrderVO } from '@/api/scm/mfg/workorder'

// 扩展ProductReceiptVO以包含明细数组
interface ExtendedProductReceiptVO extends ProductReceiptVO {
  productReceiptDetails?: any[]
  details?: any[] // 添加 details 字段
}
/** 产品入库 列表 */
defineOptions({ name: 'ProductReceipt' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const { push } = useRouter() // 路由
const { copy } = useClipboard() // 复制功能

/** 复制订单单号 */
const copyOrderNo = async (orderNo: string) => {
  try {
    await copy(orderNo)
    message.success('单号复制成功')
  } catch (error) {
    message.error('复制失败')
  }
}

// ========== 待入库相关方法 ==========

/** 获取生产单列表 */
const getWorkOrderList = async () => {
  workOrderLoading.value = true
  try {
    const data = await WorkOrderApi.getWorkOrderPage(workOrderQueryParams)
    workOrderList.value = data.list
    workOrderTotal.value = data.total
  } finally {
    workOrderLoading.value = false
  }
}





/** 生产单汇总方法 */
const workOrderSummaryMethod = (param: any) => {
  const { columns } = param
  const sums: string[] = []

  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }

    // 对数量相关字段进行汇总
    if (['orderQuantity', 'scheduleQuantity', 'schedulePiece', 'actualQuantity', 'actualPiece'].includes(column.property)) {
      const values = workOrderList.value
        .map(item => Number(item[column.property]))
        .filter(value => !isNaN(value))

      if (values.length > 0) {
        sums[index] = values.reduce((prev, curr) => prev + curr, 0).toString()
      } else {
        sums[index] = ''
      }
    } else {
      sums[index] = ''
    }
  })

  return sums
}

/** 生产单选择变化处理 */
const handleWorkOrderSelectionChange = (selection: any[]) => {
  // 处理选择变化
}



/** 获取生产单单位名称 */
const getWorkOrderUnitName = (unitId: number) => {
  return unitMap.value.get(unitId) || unitId?.toString() || '-'
}

/** 打开产品入库表单 */
const openProductReceiptForm = (type: string, workOrder: any) => {
  // 传递生产单信息给表单组件
  formRef.value.open(type, undefined, workOrder)
}

// Tab相关
const subTabsName = ref('pendingReceipt') // 当前选中的tab

// 待入库相关
const workOrderLoading = ref(false) // 生产单加载状态
const workOrderList = ref<WorkOrderVO[]>([]) // 生产单列表
const workOrderTotal = ref(0) // 生产单总数
const workOrderQueryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  approveStatus: '3', // 审核状态为通过
  status: '4' // 生产状态为已完成
})

// 已入库相关
const loading = ref(true) // 列表的加载中
const list = ref<ExtendedProductReceiptVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const spanArr = ref<number[]>([]) // 行合并数组
const currentRow = ref() // 当前选中行
const isExpanded = ref(false) // 表单展开状态
const unitMap = ref<Map<number, string>>(new Map()) // 单位ID到名称的映射
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  materialName: undefined,
  materialCode: undefined,
  orderNo: undefined,
  bizType: undefined,
  sourceType: undefined,
  sourceId: undefined,
  sourceNo: undefined,
  objectId: undefined,
  objectName: undefined,
  objectOrderNo: undefined,
  date: [],
  warehouseId: undefined,
  accountId: undefined,
  note: undefined,
  remark: undefined,
  createTime: [],
  approveStatus: undefined,
  deptId: undefined,
  empId: undefined,
  managerId: undefined,
  manger1Id: undefined,
  accountantId: undefined,
  checkerId: undefined,
  detail:true
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const selectedRows = ref<ExtendedProductReceiptVO[]>([]) // 选中的行
const approveInfoFormRef = ref() // 审核弹窗引用
const approve_status = getStrDictOptions(DICT_TYPE.APPROVE_STATUS)
const material_source = getStrDictOptions(DICT_TYPE.MATERIAL_SOURCE)
const inventory_transaction_type = getStrDictOptions(DICT_TYPE.INVENTORY_TRANSACTION_TYPE)

/** 扁平化数据 - 将主单据和明细数据合并 */
const flattenedList = computed(() => {
  const result: any[] = []
  spanArr.value = [] // 每次重新计算时清空旧数据

  list.value.forEach(order => {
    const details = order.productReceiptDetails?.length ? order.productReceiptDetails : [{}] // 确保无明细时也有占位行
    const detailCount = details.length

    details.forEach((detail: any, index: number) => {
      result.push({ ...order, detail })
      // 主信息列只在第一个明细行合并，合并行数=明细数量
      spanArr.value.push(index === 0 ? detailCount : 0)
    })
  })

  return result
})

/** 行合并方法 */
const objectSpanMethod = ({ row, column, rowIndex }: any) => {
  // 需要合并的主信息列
  const mergeFields = ['orderNo', 'bizType', 'sourceType', 'sourceNo', 'objectName', 'date', 'approveStatus', 'objectOrderNo', 'note', 'remark', 'createTime', 'operation']

  // 检查是否是操作列（通过列标签判断）
  const isOperationColumn = column.label === '操作'

  // 检查是否是选择列（通过列类型判断）
  const isSelectionColumn = column.type === 'selection'

  if (mergeFields.includes(column.property) || isOperationColumn || isSelectionColumn) {
    const span = spanArr.value[rowIndex]
    if (span > 0) {
      return {
        rowspan: span,
        colspan: 1
      }
    } else {
      return {
        rowspan: 0,
        colspan: 0
      }
    }
  }
}

/** 当前行变化 */
const handleCurrentChange = (row: any) => {
  currentRow.value = row
}

/** 选择变化处理 */
const handleSelectionChange = (selection: ExtendedProductReceiptVO[]) => {
  selectedRows.value = selection
}

/** 切换表单展开状态 */
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

/** 加载单位数据 */
const loadUnits = async () => {
  try {
    // 批量获取所有单位信息
    const units = await getRemoteUnit()

    if (!units || units.length === 0) {
      return
    }

    // 建立单位映射
    units.forEach((unit: any) => {
      if (unit && unit.id && unit.name) {
        unitMap.value.set(unit.id, unit.name)
      }
    })
  } catch (error) {
    console.error('加载单位数据失败:', error)
  }
}

/** 获取单位名称 */
const getUnitName = (unitId: number | string) => {
  if (!unitId) return ''
  const id = typeof unitId === 'string' ? parseInt(unitId) : unitId
  const unitName = unitMap.value.get(id)
  return unitName || unitId.toString()
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ProductReceiptApi.getProductReceiptPage(queryParams)
    list.value = data.list
    total.value = data.total

    // 直接使用列表中的 details 字段作为明细数据
    list.value.forEach(order => {
      // 将 details 字段赋值给 productReceiptDetails，保持原有的属性名
      order.productReceiptDetails = order.details || []
    })
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ProductReceiptApi.deleteProductReceipt(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ProductReceiptApi.exportProductReceipt(queryParams)
    download.excel(data, '产品入库.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 表格汇总方法 */
const summaryMethod = ({ columns, data }: { columns: any[], data: any[] }) => {
  const sums: any[] = []

  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }

    // 需要汇总的数量字段
    const quantityFields = ['detail.fulfilledQuantity', 'detail.plannedQuantity',
                           'detail.standardFulfilledQuantity', 'detail.standardPlannedQuantity',
                           'detail.invoiceQuantity']

    // 需要汇总的金额字段
    const amountFields = ['detail.unitPrice', 'detail.amount', 'detail.taxPrice',
                         'detail.taxAmount', 'detail.invoiceAmount']

    if (quantityFields.includes(column.property)) {
      // 数量字段汇总
      const values = data.map(item => {
        const value = column.property.split('.').reduce((obj, key) => obj?.[key], item)
        return Number(value) || 0
      })
      const total = values.reduce((prev, curr) => prev + curr, 0)
      sums[index] = formatQuantity(total)
    } else if (amountFields.includes(column.property)) {
      // 金额字段汇总
      const values = data.map(item => {
        const value = column.property.split('.').reduce((obj, key) => obj?.[key], item)
        return Number(value) || 0
      })
      const total = values.reduce((prev, curr) => prev + curr, 0)
      sums[index] = formatAmount(total)
    } else {
      // 其他字段不汇总
      sums[index] = ''
    }
  })

  return sums
}

/** 审核按钮操作 */
const handleApproval = async () => {
  console.log('handleApproval', selectedRows.value)
  if(!selectedRows.value || selectedRows.value.length === 0) {
    message.error('请选择要审核的产品入库单！')
    return
  }

  // 如果选中了多个产品入库单，提示用户
  if(selectedRows.value.length > 1) {
    message.warning('当前只支持单个产品入库单审核，请选择一个产品入库单进行审核')
    return
  }

  // 设置当前行为选中的第一个产品入库单
  const selectedOrder = selectedRows.value[0]
  currentRow.value = selectedOrder

  // 检查审核状态，如果已经审核通过，则不允许再次审核
  if (selectedOrder.approveStatus?.toString() === '3') {
    message.warning(`产品入库单 "${selectedOrder.orderNo}" 已经审核通过，不能重复审核！`)
    return
  }

  // 发起审批，传入业务数据
  approveInfoFormRef.value.open('approve', undefined, {
    bizId: selectedOrder.id,
    bizNo: selectedOrder.orderNo,
    bizType: 'product_receipt'
  })
}

/** 打开详情页面 */
const openDetail = (id: number, orderNo?: string) => {
  // 跳转到详情页面，传递标题参数
  const title = orderNo || '产品入库详情'
  push({
    name: 'ProductReceiptDetail',
    params: { id: id.toString() },
    query: { title }
  })
}



/** 初始化 **/
onMounted(async () => {
  // 先加载单位数据
  await loadUnits()
  // 根据当前tab加载对应数据
  if (subTabsName.value === 'pendingReceipt') {
    getWorkOrderList()
  } else {
    getList()
  }
})

/** 监听tab切换 */
watch(subTabsName, (newVal) => {
  if (newVal === 'pendingReceipt') {
    getWorkOrderList()
  } else {
    getList()
  }
})
</script>

<style scoped lang="scss">
/* 生产单号容器样式 */
.work-no-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  padding: 4px 0;
}

/* 生产单号文字样式 */
.work-no-text {
  font-weight: 600;
  color: #409eff;
}

/* 单号容器样式 */
.order-no-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  padding: 4px 0;
}

/* 订单号行布局 */
.order-no-cell {
  display: flex;
  align-items: center;
  width: 100%;
  min-height: 20px;
  gap: 5px; /* 文字和图标之间保持一点点间隔 */
}

.order-no-content {
  flex: 0 1 auto; /* 改为自适应宽度，不占满剩余空间 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: calc(100% - 16px); /* 为图标预留空间 */
}

/* 复制按钮样式 */
.copy-btn {
  padding: 2px !important;
  height: 20px !important;
  min-height: 20px !important;
  width: 20px !important;
  font-size: 12px;
  opacity: 0.6;
  transition: opacity 0.2s;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}



.copy-btn:hover {
  opacity: 1;
}

/* 状态标签样式 */
.status-tag :deep(.el-tag) {
  font-size: 9px !important;
  padding: 1px 4px !important;
  height: 16px !important;
  line-height: 14px !important;
  border-radius: 6px !important;
  font-weight: 500 !important;
  flex-shrink: 0;
}
</style>
